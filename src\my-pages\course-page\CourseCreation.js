import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Image,
  Input,
  Select,
  Stack,
  Text,
  Tooltip,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  UnorderedList,
  ListItem,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  HStack,
  Tag,
  Checkbox,
  MenuList,
  Menu,
  MenuButton,
} from "@chakra-ui/react";
import { FaChevronDown } from "react-icons/fa";
import { IoMdArrowRoundBack } from "react-icons/io";
import { Link, useNavigate, useParams } from "react-router-dom";
import ReactQuill from "react-quill";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./datepicker.css";
import axios from "axios";

import "react-quill/dist/quill.snow.css";


const proficiencyLevelOption = [
  { id: 0, name: "Beginner", value: "beginner" },
  { id: 1, name: "Intermediate", value: "intermediate" },
  { id: 2, name: "Advance", value: "advance" },
];

const CourseCreation = () => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImagesError, setSelectedImagesError] = useState(false);
  const [courseName, setCourseName] = useState("");
  const [courseNameError, setCourseNameError] = useState(false);
  const [isItCamp, setIsItCamp] = useState(false);
  const [campName, setCampName] = useState("");
  const [campNameError, setCampNameError] = useState(false);
  const [maximumGroupSize, setMaximumGroupSize] = useState(1);
  const [maximumGroupSizeError, setMaximumGroupSizeError] = useState(false);
  const [sessionType, setSessionType] = useState("Group");
  const [categoryType, setCategoryType] = useState("");
  const [categories, setCategories] = useState([]);
  const [courseDescription, setCourseDescription] = useState("");
  const [courseDescriptionError, setCourseDescriptionError] = useState(false);
  const [courseAmeneties, setCourseAmeneties] = useState("");
  const [btnIsLoading, setBtnIsLoading] = useState(false);
  const [facilities, setFacilities] = useState([]);
  const [selectedDays, setSelectedDays] = useState([]);
  const [isEnd, setIsEnd] = useState(false);
  const [timeFrom, setTimeFrom] = useState("");
  const [timeTo, setTimeTo] = useState("");
  const [classType, setClassType] = useState("class");
  const [startDate, setStartDate] = useState("");
  const [startDateError, setStartDateError] = useState(false);
  const [endDate, setEndDate] = useState("");
  const [price, setPrice] = useState("");
  const [priceError, setPriceError] = useState(false);
  const [fees30, setFees30] = useState("");
  const [fees60, setFees60] = useState("");
  const [fees30Error, setFees30Error] = useState(false);
  const [fees60Error, setFees60Error] = useState(false);
  const [cancellationPolicy, setCancellationPolicy] = useState("");
  const [carryThings, setCarryThings] = useState("");
  const [timeFromError, setTimeFromError] = useState(false);
  const [timeToError, setTimeToError] = useState(false);
  const [courseFacility, setCourseFacility] = useState("");
  const [categoryError, setCategoryError] = useState(false);
  const [facilityError, setFacilityError] = useState(false);
  const [selectedDaysError, setSelectedDaysError] = useState(false);
  const [coach, setCoach] = useState({});
  const [proficiencyLevel, setProficiencyLevel] = useState([]);
  const [showSlotConflict, setShowSlotConflict] = useState(false);
  const [conflictResult, setConflictResult] = useState({});
  const [academyStartTime, setAcademyStartTime] = useState(null);
  const [academyEndTime, setAcademyEndTime] = useState(null);
  const [kycChecked, setKycChecked] = useState(false);

  const toast = useToast();
  const navigate = useNavigate();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  const date = new Date(startDate);
  const today = new Date();

  const includesToday =
    date.getDate() <= today.getDate() &&
    date.getMonth() <= today.getMonth() &&
    date.getFullYear() <= today.getFullYear();

  const filterPassedTime = (time) => {
    if (!timeFrom) return true;
    const selectedTime = new Date(time);
    const minAllowedTime = getMinTime();
    return selectedTime >= minAllowedTime;
  };

  const minTime = includesToday ? currentTime : undefined;

  const getMinTime = () => {
    if (timeFrom) {
      const selectedTime = new Date(timeFrom);
      // Add 10 minutes to the start time as minimum end time
      selectedTime.setMinutes(selectedTime.getMinutes() + 10);
      return selectedTime;
    }
    return academyStartTime || new Date(0, 0, 0, 0, 0);
  };

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = new Date(currentYear, 11, 31); // Set to December 31st of the current year
    return formatDateToYYYYMMDD(lastDate);
  }

  const getTime = (time) => {
    const tempdate = new Date(time);

    const hours = tempdate.getHours(); // Get the hour component (0-23)
    const minutes = tempdate.getMinutes(); // Get the minute component (0-59)

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    return formattedTime;
  };

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const start = new Date(startDate);
    const end = new Date(endDate);

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }

  // ### Final Save ###
  const saveHandler = async () => {
    setBtnIsLoading(true);
    let lastDateOfYear = getLastDateOfCurrentYear();
    lastDateOfYear = formatDateToYYYYMMDD(lastDateOfYear);
    
    if (sessionType?.name === "Individual") {
      setMaximumGroupSize(1);
    }

    if (courseName.length <= 3) {
      setBtnIsLoading(false);
      setCourseNameError(true);
    }

    if (!courseDescription || courseDescription === "<p><br></p>") {
      setCourseDescriptionError(true);
      setBtnIsLoading(false);
    }

    if (!startDate) {
      setBtnIsLoading(false);
      setStartDateError(true);
    }

    if (!selectedDays.length) {
      setBtnIsLoading(false);
      setSelectedDaysError(true);
    }

    if (!timeFrom || !timeTo) {
      if (!timeTo) {
        setTimeToError(true);
      }
      if (!timeFrom) {
        setTimeFromError(true);
      }
      setBtnIsLoading(false);
    }

    if (classType === "class") {
      if (fees30 === "" && !( fees60)) {
        setFees30Error(true);
      }
      if (fees60 === "" && !( fees30)) {
        setFees60Error(true);
      }
      if (fees30 === ""  || fees60 === "") {
        setBtnIsLoading(false);
      }
    }

    if (classType !== "class") {
      if (price === "") {
        setBtnIsLoading(false);
        setPriceError(true);
      }
    }

    if (!categoryType) {
      setCategoryError(true);
      setBtnIsLoading(false);
    }

    if (!courseFacility) {
      setFacilityError(true);
      setBtnIsLoading(false);
    }

    if (classType === "course" && isItCamp ? !campName : false) {
      setCampNameError(true);
      setBtnIsLoading(false);
    }

    if (
      Number(maximumGroupSize) &&
      Math.sign(Number(maximumGroupSize)) === -1 &&
      sessionType === "group" &&
      classType === "course"
    ) {
      setMaximumGroupSizeError(true);
      setBtnIsLoading(false);
    }

    // Validations //
    if (!id) {
      setBtnIsLoading(true);
      toast({
        title: "Please select coach, before creating the course",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    if (
      daysDifference(
        new Date(startDate),
        endDate ? new Date(endDate) : new Date(lastDateOfYear),
        selectedDays
      ) === 0
    ) {
      setBtnIsLoading(false);
      toast({
        title: "Selected Days is not available between selected date range",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    const startDateTime = `${formatDateToYYYYMMDD(startDate)}T${getTime(
      timeFrom
    )}:00`;
    const endDateTime = `${
      endDate
        ? formatDateToYYYYMMDD(endDate)
        : formatDateToYYYYMMDD(lastDateOfYear)
    }T${getTime(timeTo)}:00`;

    let isAvailable = await getAvailableSlots(`${id}`);

    if (!isAvailable) {
      setBtnIsLoading(false);
      toast({
        title: "Conflicting time slots",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    const selectedUrl = categories.filter((x) => x.name === categoryType);

    // main working body //
    let raw = JSON.stringify({
      courseName: `${courseName.replace(/\s+/g, " ").trim()}`,
      description: `${courseDescription}`,
      images:
        selectedImages.length <= 0
          ? [{ url: selectedUrl[0]?.image }]
          : selectedImages,
      coach_id: `${id}` || "",
      coachName: (coach?.firstName || "") + " " + (coach?.lastName || ""),
      category: `${categoryType}`,
      customImage: selectedImages.length <= 0 ? false : true,

      sessionType:
        classType === "course" ? `${sessionType.toLowerCase()}` : "individual",
      classType: classType,
      camp: isItCamp,
      campName: `${campName.replace(/\s+/g, " ").trim()}`,
      fees: {
        feesCourse: classType !== "class" ? Number(price) : "",
        fees30: classType === "class" ? Number(fees30) : "",
        fees60: classType === "class" ? Number(fees60) : "",
        fees:
          classType === "class"
            ? fees30 && fees60
              ? Number(fees30)
              : fees30 && fees60
              ? Number(fees60)
              : fees30 && fees60
            : Number(price),
      },
      maxGroupSize: maximumGroupSize !== "" ? maximumGroupSize : 1,
      amenitiesProvided: courseAmeneties,
      proficiency: proficiencyLevel,
      facility: { name: courseFacility },
      coachEmail: coach.email,
      dates: {
        startDate: startDateTime,
        endDate: endDateTime,
        startTime: getTime(timeFrom),
        endTime: getTime(timeTo),
        days: selectedDays,
      },
      whatYouHaveToBring: carryThings,
      cancellationPolicy: cancellationPolicy,
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: raw,
    };

    axios
      .request(config)
      .then((response) => {
        toast({
          title: "Course created successfully",
          status: "success",
          duration: 4000,
          isClosable: true,
          position: "top",
        });
        navigate(`/course-page`);
        setBtnIsLoading(false);
        setCourseDescriptionError(false);
        setCourseName("");
        setCourseNameError(false);
        setCampName("");
        setCampNameError(false);
        setMaximumGroupSize("");
        setMaximumGroupSizeError(false);
        setCourseDescription("");
        setCourseAmeneties("");
        setStartDateError(false);
        setEndDate("");
        setPriceError(false);
        setFees30("");
        setFees60("");
        setStartDate("");
        setTimeFrom("");
        setSelectedDaysError(false);
        setSelectedDays([]);
        setCancellationPolicy("");
        setCarryThings("");
        setCategoryType({});
        setCourseFacility({});
        setTimeTo("");
        setFees30Error(false);
        setFees60Error(false);
        setSelectedImages([]);
        setCategoryError(false);
        setFacilityError(false);
        setBtnIsLoading(false);
        setSelectedImagesError(false);
      })
      .catch((error) => {
        setBtnIsLoading(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: error.response.data.error,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCategories = async () => {
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/category`
      );
      setCategories(response.data.data);
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const getCoachDetails = () => {
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        const coachData = response?.data;
        
        // Check if KYC documents are available - only check once
        if (!kycChecked && (!coachData?.kycDocuments?.documentImg || coachData.kycDocuments.documentImg.length === 0)) {
          setKycChecked(true);
          toast({
            title: "KYC Documents Required",
            description: "Coach must complete KYC verification before creating courses. Please upload KYC documents first.",
            status: "error",
            duration: 6000,
            position: "top",
            isClosable: true,
          });
          navigate(`/coach-page/details/${id}`);
          return;
        }
        
        setFacilities(coachData?.linkedFacilities);
        setCoach(coachData);
        
        if (coachData?.academyAvailability) {
          const { startTime, endTime } = coachData.academyAvailability;
          if (startTime && endTime) {
            const [sh, sm] = startTime.split(":");
            const [eh, em] = endTime.split(":");
            
            // Create proper Date objects for today's date with the specified times
            const today = new Date();
            const startTimeObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), Number(sh), Number(sm) || 0);
            const endTimeObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), Number(eh), Number(em) || 0);
            
            setAcademyStartTime(startTimeObj);
            setAcademyEndTime(endTimeObj);
            
            console.log('Academy Start Time:', startTimeObj);
            console.log('Academy End Time:', endTimeObj);
          }
        }
      })
      .catch((error) => {
        console.log(error);
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleRadioChange = (e) => {
    const value = e.target.value === "yes";
    setIsItCamp(value);
  };

  // Fix for the time picker filters
  const filterTimeForStart = (time) => {
    const selectedTime = new Date(time);

    // If academy times aren't set, allow any time
    if (!academyStartTime || !academyEndTime) {
      return true;
    }

    // Convert academy times to Date objects for comparison
    const academyStart = new Date(academyStartTime);
    const academyEnd = new Date(academyEndTime);

    // Check if selected time is within academy hours
    return selectedTime >= academyStart && selectedTime <= academyEnd;
  };

  const filterTimeForEnd = (time) => {
    const selectedTime = new Date(time);

    // If academy times aren't set, allow any time
    if (!academyStartTime || !academyEndTime) {
      return true;
    }

    // Convert academy times to Date objects for comparison
    const academyStart = new Date(academyStartTime);
    const academyEnd = new Date(academyEndTime);

    // Additional check for minimum time (10 minutes after start time)
    const minAllowedTime = timeFrom
      ? new Date(new Date(timeFrom).getTime() + 10 * 60000)
      : academyStart;

    return selectedTime >= minAllowedTime && selectedTime <= academyEnd;
  };

  // Update your DatePicker components like this:

  function getLastDayOfYear() {
    let currentDate = new Date();
    currentDate.setMonth(11);
    currentDate.setDate(31);
    return currentDate.toISOString();
  }

  const getAvailableSlots = async (id, value, endTime) => {
    let lastDateOfYear = getLastDateOfCurrentYear();
    const startDateTime = `${formatDateToYYYYMMDD(startDate)}T${getTime(
      timeFrom
    )}:00`;
    const endDateTime = `${
      endDate ? formatDateToYYYYMMDD(endDate) : lastDateOfYear
    }T${timeTo !== "" ? getTime(timeTo) : getTime(endTime)}:00`;

    let data = JSON.stringify({
      dates: {
        startDate: startDateTime,
        endDate: endDateTime,
        startTime: getTime(timeFrom),
        endTime: endTime ? getTime(endTime) : getTime(timeTo),
        days: selectedDays,
      },
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course/availableSlots/${id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    let response = await axios.request(config);

    setConflictResult(response.data);
    if (response.data.message === "Slots available") {
      setShowSlotConflict(false);
      return true;
    } else {
      setShowSlotConflict(true);
      return false;
    }
  };

  const formatSlotDate = (date) => {
    const originalDateString = date;
    const originalDate = new Date(originalDateString);

    const year = originalDate.getUTCFullYear();
    const month = originalDate.getUTCMonth() + 1;
    const day = originalDate.getUTCDate();

    const formattedDateString = `${day}-${
      month < 10 ? "0" : ""
    }${month}-${year}`;

    return formattedDateString;
  };

  const handleFileChange = async (e) => {
    try {
      const file = e.currentTarget.files[0];
      setSelectedImagesError(false);

      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        toast({
          title: "Image Uploaded Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title:
            "Something went wrong while uploading image, please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }

      setSelectedImages([...selectedImages, { url: url }]);
    } catch (error) {
      toast({
        title:
          "Something went wrong while uploading image, please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const deleteImageFiles = async (url, index) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      toast({
        title: "Image removed Successfully",
        status: "success",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } catch (error) {
      console.log(error);
      toast({
        title:
          "Something went wrong while removing image, please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    getCategories();
    getCoachDetails();
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Create Training Schedule" content="container">
        {/* Breadcrumb */}
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={4}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"26px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem>
                <Link to="/course-page">Course</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Course</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
        </Flex>
        <Card bgColor={"#f9f9f9"}>
          <CardBody>
            <Card>
              <CardBody>
                {/* Course name */}
                <FormControl isInvalid={courseNameError}>
                  <FormLabel>Training Schedule Name</FormLabel>
                  <Input
                    type="text"
                    placeholder="Enter training schedule name"
                    name="course-name"
                    id="course-name"
                    min={10}
                    value={courseName}
                    autoComplete="off"
                    onChange={(e) => {
                      setCourseName(e.target.value);
                      if (e.target.value !== "") {
                        setCourseNameError(false);
                      } else {
                        setCourseNameError(true);
                      }
                      if (
                        e.target.value.length > 100 ||
                        e.target.value.length < 2
                      ) {
                        setCourseNameError(true);
                      } else {
                        setCourseNameError(false);
                      }
                    }}
                  />
                  {courseNameError && (
                    <FormErrorMessage>
                      Please enter training schedule name, atleast 3 characters and maximum
                      100 characters
                    </FormErrorMessage>
                  )}
                </FormControl>
                {/* Description */}
                <FormControl mt={6} isInvalid={courseDescriptionError}>
                  <FormLabel>Description</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={courseDescription}
                    onChange={(value) => {
                      setCourseDescription(value);
                      if (value === "" || value === "<p><br></p>") {
                        setCourseDescriptionError(true);
                      } else {
                        setCourseDescriptionError(false);
                      }
                    }}
                  />
                  {courseDescriptionError && (
                    <FormErrorMessage>
                      Please enter description
                    </FormErrorMessage>
                  )}
                </FormControl>
              </CardBody>
            </Card>
            <Card mt={4}>
              <CardBody>
                {/* Course Image */}
                <FormControl mt={6}>
                  <FormLabel>Course Image</FormLabel>
                  <Box mt={3}>
                    <Input
                      id={`kycDocuments.documentImg.${0}.url`}
                      name={`kycDocuments.documentImg.${0}.url`}
                      type="file"
                      accept="image/*"
                      disabled={selectedImages.length > 0}
                      onChange={(e) => handleFileChange(e, 0)}
                    />
                    {selectedImagesError && (
                      <Text color={"red.500"} fontSize={"sm"} mt={1}>
                        Please select atleast one document image
                      </Text>
                    )}
                    {selectedImages.length > 0 && (
                      <Flex wrap={"wrap"} mt={3}>
                        {selectedImages.map((preview, index) => (
                          <Box key={index} m={1} textAlign={"center"}>
                            <a
                              href={`${preview?.url}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Image
                                src={preview?.url}
                                alt={`Preview ${index}`}
                                height="11vh"
                                width="7vw"
                              />
                            </a>
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                deleteImageFiles(preview?.url, index);
                                setSelectedImages(
                                  selectedImages.filter(
                                    (image) => image.url !== preview.url
                                  )
                                );
                              }}
                            >
                              Remove
                            </Button>
                          </Box>
                        ))}
                      </Flex>
                    )}
                  </Box>
                </FormControl>
              </CardBody>
            </Card>

            {/*  Slot */}
            <Card mt={4}>
              <CardBody>
                {/* Radion btn - session type */}
                <Flex justifyContent={"center"} alignItems={"center"}>
                  <Stack direction="row">
                    <Box mr={6}>
                      <input
                        id="notification-method-class"
                        type="radio"
                        value=""
                        required
                        checked={classType === "class"}
                        onChange={(e) => {
                          e.target.checked && setClassType("class");
                          endDate ? setIsEnd(true) : setIsEnd(false);
                        }}
                      />
                      <label
                        htmlFor="notification-method-class"
                        className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                        style={{
                          cursor: "pointer",
                          fontSize: "1.1rem",
                          fontWeight: "600",
                        }}
                      >
                        Session
                      </label>
                    </Box>
                    <Box>
                      <input
                        id="notification-method-course"
                        type="radio"
                        value=""
                        required
                        checked={classType === "course"}
                        onChange={(e) => {
                          e.target.checked && setClassType("course");
                          setIsEnd(true);
                        }}
                      />
                      <label
                        htmlFor="notification-method-course"
                        className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                        style={{
                          cursor: "pointer",
                          fontSize: "1.1rem",
                          fontWeight: "600",
                        }}
                      >
                        Course
                      </label>
                    </Box>
                  </Stack>
                </Flex>
                {/* Start Date */}
                <FormControl my={3} isInvalid={startDateError}>
                  <FormLabel>Start Date</FormLabel>
                  <DatePicker
                    placeholderText="Select start date"
                    className={
                      startDateError
                        ? "my-custom-datepicker dateError"
                        : "my-custom-datepicker"
                    }
                    selected={startDate}
                    onChange={(date) => {
                      setEndDate("");
                      setStartDate(date);
                      if (date !== "") {
                        setStartDateError(false);
                      } else {
                        setStartDateError(true);
                      }
                    }}
                    minDate={new Date()}
                    dateFormat="dd MMMM yyyy"
                  />
                  {startDateError && (
                    <FormErrorMessage>
                      Please select start date
                    </FormErrorMessage>
                  )}
                </FormControl>
                {/* Select Days */}
                <FormControl my={4} isInvalid={selectedDaysError}>
                  <FormLabel>Select Days</FormLabel>
                  <Stack spacing={5} direction="row">
                    {days.map((day, i) => (
                      <Button
                        key={i}
                        type="button"
                        onClick={() => {
                          setSelectedDays(
                            selectedDays.includes(day)
                              ? selectedDays.filter(
                                  (selected) => selected !== day
                                )
                              : [...selectedDays, day]
                          );
                          if (!day) {
                            setSelectedDaysError(true);
                          } else {
                            setSelectedDaysError(false);
                          }
                        }}
                        colorScheme={
                          selectedDays.includes(day) ? "telegram" : "gray"
                        }
                      >
                        {day}
                      </Button>
                    ))}
                  </Stack>
                  {selectedDaysError && (
                    <FormErrorMessage>Select the Days</FormErrorMessage>
                  )}
                </FormControl>
                <Divider />
                {/* End Date - radio btn */}

                <Box>
                  {classType === "class" && (
                    <FormLabel mt={3}>End Date</FormLabel>
                  )}
                  <Flex
                    justifyContent={"flex-start"}
                    alignItems={"center"}
                    mb={3}
                  >
                    {classType === "class" && (
                      <Stack direction="row">
                        <Box mr={6}>
                          <input
                            id="horizontal-list-radio-license-never"
                            type="radio"
                            required
                            value=""
                            defaultChecked={!isEnd}
                            onChange={(e) => {
                              e.target.checked
                                ? setIsEnd(false)
                                : setIsEnd(true);
                            }}
                            name="list-radio"
                          />
                          <label
                            htmlFor="horizontal-list-radio-license-never"
                            style={{
                              marginLeft: "8px",
                              cursor: "pointer",
                              fontSize: "1rem",
                              fontWeight: "600",
                            }}
                          >
                            Never
                          </label>
                        </Box>
                        <Box>
                          <input
                            id="horizontal-list-radio-license-on"
                            type="radio"
                            required
                            value=""
                            defaultChecked={isEnd}
                            onChange={(e) => {
                              e.target.checked
                                ? setIsEnd(true)
                                : setIsEnd(false);
                            }}
                            name="list-radio"
                          />
                          <label
                            htmlFor="horizontal-list-radio-license-on"
                            style={{
                              marginLeft: "8px",
                              cursor: "pointer",
                              fontSize: "1rem",
                              fontWeight: "600",
                            }}
                          >
                            On
                          </label>
                        </Box>
                      </Stack>
                    )}
                  </Flex>
                  {/* when end date is on*/}
                  {isEnd && (
                    <FormControl my={3}>
                      <FormLabel>Select End Date</FormLabel>
                      {/* <Input
                        type="date"
                        placeholder="Select start date"
                        id="start_date"
                        onChange={(e) => setEndDate(e.target.value)}
                        required
                        minDate={new Date()}
                      /> */}
                      <DatePicker
                        placeholderText="Select end date"
                        className={
                          startDateError
                            ? "my-custom-datepicker dateError"
                            : "my-custom-datepicker"
                        }
                        selected={endDate}
                        onChange={(date) => {
                          setEndDate(date);
                        }}
                        minDate={
                          startDate
                            ? new Date(startDate).setDate(
                                new Date(startDate).getDate() + 1
                              )
                            : new Date()
                        }
                        dateFormat="dd MMMM yyyy"
                      />
                    </FormControl>
                  )}
                </Box>
                {/* Start Time && End time */}
                <Divider />
                <Flex justifyContent={"space-between"} alignItems={"center"}>
                  <FormControl
                    my={3}
                    flexBasis={"48%"}
                    isInvalid={timeFromError}
                  >
                    <FormLabel>Start Time</FormLabel>
                    <DatePicker
                      selected={timeFrom}
                      className={
                        timeFromError
                          ? "my-custom-datepicker dateError"
                          : "my-custom-datepicker"
                      }
                      onKeyDown={(e) => e.preventDefault()}
                      onChange={async (value) => {
                        if (value === "") {
                          setTimeFromError(true);
                        } else {
                          setTimeFromError(false);
                        }
                        setTimeFrom(value);
                        setTimeTo("");
                      }}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={30}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      placeholderText="Select time"
                      disabled={startDate === ""}
                      filterTime={filterTimeForStart}
                    />

                  
                  </FormControl>
                  <FormControl my={3} flexBasis={"48%"} isInvalid={timeToError}>
                    <FormLabel>End Time</FormLabel>
                    <DatePicker
                      selected={timeTo}
                      onKeyDown={(e) => e.preventDefault()}
                      onChange={async (value) => {
                        await getAvailableSlots(`${id}`, "", value);
                        setTimeTo(value);
                        if (value === "") {
                          setTimeToError(true);
                        } else {
                          setTimeToError(false);
                        }
                      }}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={30}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      className={
                        timeToError
                          ? "my-custom-datepicker dateError"
                          : "my-custom-datepicker"
                      }
                      placeholderText="Select time"
                      disabled={timeFrom === ""}
                      filterTime={filterTimeForEnd}
                    />
                    {timeToError && (
                      <FormErrorMessage>Select the time</FormErrorMessage>
                    )}
                  </FormControl>
                </Flex>
                {/* Conflicting Dates */}
                {showSlotConflict && (
                  <Box my={4}>
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mb={2}
                    >
                      <Text
                        color={"red.500"}
                        fontSize={"lg"}
                        fontWeight={"semibold"}
                      >
                        Conflicting Courses
                      </Text>
                    </Flex>
                    <TableContainer>
                      <Table variant="simple">
                        <Thead
                          bgColor={"#c1eaee"}
                          position={"sticky"}
                          top={"0px"}
                        >
                          <Tr bgColor={"red.100"}>
                            <Th>Start Date</Th>
                            <Th>End Date</Th>
                            <Th>Start Time</Th>
                            <Th>End Time</Th>
                            <Th>Days</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {conflictResult?.conflictingDates?.map(
                            (conflictDate, inx) => {
                              return (
                                <Tr bgColor={"gray.50"} key={inx}>
                                  <Td>
                                    {formatSlotDate(conflictDate?.startDate)}
                                  </Td>
                                  <Td>
                                    {formatSlotDate(conflictDate?.endDate)}
                                  </Td>
                                  <Td>{conflictDate?.conflictingStartTime}</Td>
                                  <Td>{conflictDate?.conflictingEndTime}</Td>
                                  <Td>
                                    <UnorderedList>
                                      {conflictDate?.conflictingDays?.map(
                                        (days, i) => {
                                          return (
                                            <ListItem key={i}>{days}</ListItem>
                                          );
                                        }
                                      )}
                                    </UnorderedList>
                                  </Td>
                                </Tr>
                              );
                            }
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
                {/* price */}
                {classType === "class" ? (
                  <>
                    <Flex
                      justifyContent={"space-between"}
                      alignItems={"center"}
                    >
                      <FormControl
                        my={4}
                        flexBasis={"30%"}
                        isInvalid={fees30Error}
                      >
                        <FormLabel>
                          Price <span>(30 minutes)</span>
                        </FormLabel>
                        <Input
                          type="number"
                          name="price"
                          id="price"
                          placeholder="₹0"
                          value={fees30}
                          autoComplete="off"
                          onChange={(e) => {
                            let newValue = e.target.value;
                            newValue = newValue.replace(/[^\d.]/g, "");
                            newValue = newValue.replace(/^0+/, "");
                            newValue = newValue.replace(/^\./, "");
                            if (newValue.includes(".")) {
                              newValue = newValue.split(".")[0];
                            }
                            setFees30(newValue);
                            if (e.target.value === "" && !( fees60)) {
                              setFees30Error(true);
                            } else {
                              setFees60Error(false);
                              setFees30Error(false);
                            }
                          }}
                        />
                        {fees30Error && (
                          <FormErrorMessage>
                            Please enter the price
                          </FormErrorMessage>
                        )}
                      </FormControl>
                      <FormControl
                        my={4}
                        flexBasis={"30%"}
                        isInvalid={fees60Error}
                      >
                        <FormLabel>
                          Price <span>(60 minutes)</span>
                        </FormLabel>
                        <Input
                          type="number"
                          name="price"
                          id="price"
                          placeholder="₹0"
                          min={0}
                          step={1}
                          value={fees60}
                          autoComplete="off"
                          onChange={(e) => {
                            let newValue = e.target.value;
                            newValue = newValue.replace(/[^\d.]/g, "");
                            newValue = newValue.replace(/^0+/, "");
                            newValue = newValue.replace(/^\./, "");
                            if (newValue.includes(".")) {
                              newValue = newValue.split(".")[0];
                            }
                            setFees60(newValue);
                            if (e.target.value === "" && !(fees30)) {
                              setFees60Error(true);
                            } else {
                              setFees60Error(false);
                              setFees30Error(false);
                          
                            }
                          }}
                        />
                        {fees60Error && (
                          <FormErrorMessage>
                            Please enter price
                          </FormErrorMessage>
                        )}
                      </FormControl>
                    </Flex>
                  </>
                ) : (
                  <FormControl my={4} isInvalid={priceError}>
                    <FormLabel>Price</FormLabel>
                    <Input
                      type="number"
                      name="price"
                      id="price"
                      placeholder="₹0.00"
                      value={price}
                      autoComplete="off"
                      onChange={(e) => {
                        let newValue = e.target.value;
                        newValue = newValue.replace(/[^\d.]/g, "");
                        newValue = newValue.replace(/^0+/, "");
                        newValue = newValue.replace(/^\./, "");
                        if (newValue.includes(".")) {
                          newValue = newValue.split(".")[0];
                        }
                        setPrice(newValue);
                        if (e.target.value !== "") {
                          setPriceError(false);
                        } else {
                          setPriceError(true);
                        }
                      }}
                    />
                    {priceError && (
                      <FormErrorMessage>
                        Please enter the price
                      </FormErrorMessage>
                    )}
                  </FormControl>
                )}
              </CardBody>
            </Card>
            <Card mt={4}>
              <CardBody>
                {/* Session type if -- Course */}
                {classType === "course" && (
                  <FormControl mt={6}>
                    <FormLabel>Session Type</FormLabel>
                    <Select
                      value={sessionType}
                      onChange={(e) => {
                        setSessionType(e.target.value);
                        setMaximumGroupSize(1);
                      }}
                    >
                      <option value="Group">Group</option>
                      <option value="Individual">Individual</option>
                    </Select>
                  </FormControl>
                )}

                {/* Is it a camp if --- Course */}
                {classType !== "class" && (
                  <>
                    <Flex
                      justifyContent={"flex-start"}
                      alignItems={"center"}
                      mt={4}
                      mb={2}
                    >
                      <Text
                        fontWeight={"semibold"}
                        fontSize={"16px"}
                        mb={1}
                        mr={4}
                      >
                        Is it a camp ?{" "}
                      </Text>
                      <Box mr={4}>
                        <input
                          type="radio"
                          id="yes"
                          name="camp-type"
                          value="yes"
                          onChange={handleRadioChange}
                          style={{ marginTop: "2px" }}
                        />
                        <label
                          htmlFor="yes"
                          style={{ marginLeft: "8px", cursor: "pointer" }}
                        >
                          Yes
                        </label>
                      </Box>
                      <Box>
                        <input
                          type="radio"
                          id="no"
                          name="camp-type"
                          value="no"
                          onChange={handleRadioChange}
                          defaultChecked
                          style={{ marginTop: "2px" }}
                        />
                        <label
                          htmlFor="no"
                          style={{ marginLeft: "8px", cursor: "pointer" }}
                        >
                          No
                        </label>
                      </Box>
                    </Flex>
                    {isItCamp && (
                      <FormControl isInvalid={campNameError}>
                        <Input
                          type="text"
                          name="camp-name"
                          id="camp-name"
                          value={campName}
                          autoComplete="off"
                          placeholder="Enter camp name"
                          onChange={(e) => {
                            setCampName(e.target.value);
                            if (e.target.value !== "") {
                              setCampNameError(false);
                            } else {
                              setCampNameError(true);
                            }
                            if (
                              e.target.value.length > 100 ||
                              e.target.value.length < 3
                            ) {
                              setCampNameError(true);
                            } else {
                              setCampNameError(false);
                            }
                          }}
                        />
                        {campNameError && (
                          <FormErrorMessage>
                            Please enter camp name characters must be greater
                            than or equal to 3 and less than equal to 100
                          </FormErrorMessage>
                        )}
                      </FormControl>
                    )}
                  </>
                )}

                {/* Category */}
                <FormControl mt={6} isInvalid={categoryError}>
                  <FormLabel>Category</FormLabel>
                  <Select
                    placeholder="Select Category"
                    onChange={(e) => {
                      setCategoryType(e.target.value);
                      setCategoryError(false);
                    }}
                  >
                    {categories?.map((category, inx) => (
                      <option key={inx} value={category?.name}>
                        {category?.name}
                      </option>
                    ))}
                  </Select>
                  {categoryError && (
                    <FormErrorMessage>Please select category</FormErrorMessage>
                  )}
                </FormControl>
                {/* Select Facility */}
                <FormControl mt={6} isInvalid={facilityError}>
                  <FormLabel>Facility</FormLabel>
                  <Select
                    placeholder="Select Facility"
                    onChange={(e) => {
                      setCourseFacility(e?.target?.value);
                      setFacilityError(false);
                      setCourseAmeneties(
                        facilities.filter((x) => x.name === e.target.value)[0]
                          .amenities
                      );
                    }}
                  >
                    {facilities.map((facility, inx) => (
                      <option key={inx} value={facility.name}>
                        {facility.name}
                      </option>
                    ))}
                  </Select>
                  {facilityError && (
                    <FormErrorMessage>Please select facility</FormErrorMessage>
                  )}
                </FormControl>

                {/* Max group size */}
                {sessionType === "Group" && classType === "course" && (
                  <FormControl mt={6} isInvalid={maximumGroupSizeError}>
                    <FormLabel>Maximum Group Size</FormLabel>
                    <Input
                      type="number"
                      placeholder="Enter maximum group size"
                      name="max-group-size"
                      id="max-group-size"
                      value={maximumGroupSize}
                      onChange={(e) => {
                        setMaximumGroupSize(
                          e.target.value.replace(/\s+/g, " ").trim()
                        );
                        if (e.target.value !== "" && e.target.value !== "0") {
                          setMaximumGroupSizeError(false);
                        } else {
                          setMaximumGroupSizeError(true);
                        }
                        if (Number(e.target.value) > 500) {
                          setMaximumGroupSizeError(true);
                        } else {
                          setMaximumGroupSizeError(false);
                        }
                      }}
                    />
                    {maximumGroupSizeError && (
                      <FormErrorMessage>
                        Please enter maximum group size, must be greater than 0
                        and less than equal to 500
                      </FormErrorMessage>
                    )}
                  </FormControl>
                )}
                {/* Proficiency Level */}

                <FormControl mt={6}>
                  <FormLabel>Proficiency Level</FormLabel>
                  <Menu>
                    <MenuButton
                      as={Button}
                      w={"100%"}
                      rightIcon={<FaChevronDown />}
                    >
                      Select Proficiency
                    </MenuButton>
                    <MenuList minW={window.innerWidth - 600 + "px"}>
                      {proficiencyLevelOption?.map((proficiency, i) => (
                        <Box p={2} key={i}>
                          <Checkbox
                            size="md"
                            id={i}
                            colorScheme="green"
                            value={proficiencyLevel} // Set the value to the category itself
                            // isChecked={formik.values.sportsCategories.includes(
                            //   category.name
                            // )}
                            onChange={(value) => {
                              console.log(value.target.checked);
                              if (value.target.checked) {
                                setProficiencyLevel([
                                  ...proficiencyLevel,
                                  proficiency.value,
                                ]);
                              } else {
                                if (proficiencyLevel.length > 0) {
                                  setCategoryError(false);
                                } else {
                                  setCategoryError(true);
                                }
                              }
                            }}
                          >
                            {proficiency.name}
                          </Checkbox>
                        </Box>
                      ))}
                    </MenuList>
                  </Menu>
                  <HStack spacing={4} mt={2}>
                    {proficiencyLevel?.map((proficiency, i) => (
                      <Tag
                        size={"md"}
                        key={i}
                        variant="solid"
                        colorScheme="teal"
                      >
                        {proficiency}
                      </Tag>
                    ))}
                  </HStack>
                  {categoryError && (
                    <Text color={"red.500"} fontSize={"sm"} mt={1}>
                      Categories are required
                    </Text>
                  )}
                </FormControl>

                {/* <FormControl mt={6}>
                  <FormLabel>Proficiency Level</FormLabel>
                  <Select
                    value={proficiencyLevel}
                    onChange={(e) => setProficiencyLevel(e.target.value)}
                  >
                    <option value="Beginner">Beginner</option>
                    <option value="Intermediate">Intermediate</option>
                    <option value="Advanced">Advanced</option>
                  </Select>
                </FormControl> */}

                {/* Ameneties */}
                <FormControl mt={6}>
                  <FormLabel>Amenities</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={courseAmeneties}
                    onChange={(value) => {
                      setCourseAmeneties(value);
                    }}
                  />
                </FormControl>
                {/* Thing Have to Carry with */}
                <FormControl mt={6}>
                  <FormLabel>Thing Have to Carry with</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={carryThings}
                    onChange={(value) => {
                      setCarryThings(value);
                    }}
                  />
                </FormControl>
                {/* Cancellation Policy */}
                <FormControl mt={6}>
                  <FormLabel>Cancellation Policy</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={cancellationPolicy}
                    onChange={(value) => {
                      setCancellationPolicy(value);
                    }}
                  />
                </FormControl>
              </CardBody>
            </Card>
            <Flex mt={4} justifyContent={"space-between"} alignItems={"center"}>
              <Button
                variant={"solid"}
                colorScheme="red"
                size={"sm"}
                px={8}
                flexBasis={"49%"}
                onClick={onOpen3}
              >
                Discard
              </Button>
              <Button
                variant={"solid"}
                colorScheme="green"
                size={"sm"}
                px={8}
                ml={4}
                isLoading={btnIsLoading}
                onClick={saveHandler}
                flexBasis={"49%"}
              >
                Submit
              </Button>
            </Flex>
          </CardBody>
        </Card>

        {/* Discard */}
        <AlertDialog isOpen={isOpen3} onClose={onClose3} isCentered>
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Discard Changes
              </AlertDialogHeader>

              <AlertDialogBody>
                Are you sure? You can't undo this action afterwards.
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  colorScheme="red"
                  onClick={() => {
                    onClose3();
                  }}
                >
                  No
                </Button>
                <Button
                  colorScheme="green"
                  onClick={() => {
                    onClose3();
                    navigate(-1);
                  }}
                  ml={3}
                >
                  Yes
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Layout>
    </Box>
  );
};

export default CourseCreation;
