import React from "react";
import {
  Box,
  <PERSON>ton,
  Card,
  CardBody,
  FormLabel,
  Heading,
  Image,
  ScaleFade,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
  Text,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Flex,
} from "@chakra-ui/react";
import { LuPencilLine } from "react-icons/lu";

const KYCDetailsAcademy = ({ academyData }) => {
  // Color mode values for better theming
  const cardBg = useColorModeValue("white", "gray.800");
  const readOnlyBg = useColorModeValue("gray.50", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const labelColor = useColorModeValue("gray.600", "gray.300");

  return (
    <ScaleFade initialScale={0.9} in={true}>
      <VStack spacing={{ base: 4, md: 6 }} align="stretch">
        {/* Header Section */}
        <Card 
          bg={cardBg}
          shadow="md"
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <Flex 
              direction={{ base: "column", sm: "row" }}
              justifyContent="space-between" 
              alignItems={{ base: "flex-start", sm: "center" }}
              gap={{ base: 4, sm: 0 }}
            >
              <HStack spacing={3}>
                <Heading size={{ base: "sm", md: "md" }} color="gray.600" fontWeight="bold">
                  KYC Details
                </Heading>
              </HStack>
              <Button
                colorScheme="gray"
                variant="outline"
                leftIcon={<LuPencilLine />}
                size={{ base: "sm", md: "md" }}
                borderRadius="full"
                isDisabled={true}
                _disabled={{
                  opacity: 0.6,
                  cursor: "not-allowed"
                }}
              >
                Edit
              </Button>
            </Flex>
          </CardBody>
        </Card>

        {/* Admin contact note */}
        <Alert status="info" borderRadius="lg" variant="left-accent">
          <AlertIcon />
          <Box>
            <AlertTitle fontSize={{ base: "sm", md: "md" }}>Note:</AlertTitle>
            <AlertDescription fontSize={{ base: "sm", md: "md" }}>
              To update any of these fields please contact admin.
            </AlertDescription>
          </Box>
        </Alert>

        {/* GST Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor="gray.200"
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)",
            borderColor: "gray.300"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={{ base: 4, md: 6 }}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                GST Registration
              </Heading>
            </HStack>
            
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
              {/* GST Registration Status */}
              <Box>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                  GST Registration Status
                </FormLabel>
                <Box
                  p={{ base: 2, md: 3 }}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "32px", md: "36px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm"
                  }}
                >
                  <HStack spacing={3} wrap="wrap">
                    <Text fontSize={{ base: "sm", md: "md" }}>
                      {academyData?.hasGst ? "GST Registered" : "Not GST Registered"}
                    </Text>
                    {academyData?.hasGst && (
                      <Badge colorScheme="gray" variant="subtle" fontSize={{ base: "xs", md: "sm" }}>
                        Active
                      </Badge>
                    )}
                  </HStack>
                </Box>
              </Box>

              {/* GST Number (conditional) */}
              {academyData?.hasGst && (
                <Box>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                    GST Number
                  </FormLabel>
                  <Box
                    p={{ base: 2, md: 3 }}
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "32px", md: "36px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                    wordBreak="break-all"
                  >
                    {academyData?.gstNumber || "No GST number provided"}
                  </Box>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Bank Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor="gray.200"
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)",
            borderColor: "gray.300"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={{ base: 4, md: 6 }}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                Account Details
              </Heading>
            </HStack>
            
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
              {/* Account Holder Name */}
              <Box>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                  Account Holder Name
                </FormLabel>
                <Box
                  p={{ base: 2, md: 3 }}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "32px", md: "36px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm"
                  }}
                  wordBreak="break-word"
                >
                  {academyData?.bankDetails?.accountHolderName || "No account holder name provided"}
                </Box>
              </Box>

              {/* Account Number */}
              <Box>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                  Account Number
                </FormLabel>
                <Box
                  p={{ base: 2, md: 3 }}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "32px", md: "36px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm"
                  }}
                >
                  {academyData?.bankDetails?.accountNumber ? 
                    `****-****-${academyData.bankDetails.accountNumber.slice(-4)}` : 
                    "No account number provided"
                  }
                </Box>
              </Box>

              {/* IFSC Code */}
              <Box>
                <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                  IFSC Code
                </FormLabel>
                <Box
                  p={{ base: 2, md: 3 }}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "32px", md: "36px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm"
                  }}
                >
                  {academyData?.bankDetails?.ifsc || "No IFSC code provided"}
                </Box>
              </Box>
            </VStack>
          </CardBody>
        </Card>

        {/* PAN Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor="gray.200"
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)",
            borderColor: "gray.300"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={{ base: 4, md: 6 }}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                PAN Details
              </Heading>
            </HStack>
            
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* PAN Number */}
                <Box>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                    PAN Number
                  </FormLabel>
                  <Box
                    p={{ base: 2, md: 3 }}
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "32px", md: "36px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {academyData?.panNumber || "No PAN number provided"}
                  </Box>
                </Box>

                {/* PAN Image */}
                <Box>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                    PAN Card Images
                  </FormLabel>
                  
                  <VStack spacing={4} align="stretch">
                    {/* PAN Front Image */}
                    <Box>
                      <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "xs", md: "sm" }}>
                        Front Side
                      </Text>
                      {academyData?.panImage?.front || academyData?.panImage ? (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          borderColor={borderColor}
                          bg={readOnlyBg}
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          <HStack spacing={3} mb={3}>
                            <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                              PAN Card Front Uploaded
                            </Text>
                          </HStack>
                          <a href={academyData?.panImage?.front || academyData?.panImage} target="_blank" rel="noopener noreferrer">
                            <Image
                              src={academyData?.panImage?.front || academyData?.panImage}
                              alt="PAN Front Image Preview"
                              maxW={{ base: "150px", md: "200px" }}
                              maxH={{ base: "100px", md: "150px" }}
                              objectFit="cover"
                              borderRadius="lg"
                              border="1px"
                              borderColor="gray.200"
                              transition="all 0.2s"
                              _hover={{
                                borderColor: "gray.400",
                                transform: "scale(1.02)"
                              }}
                              cursor="pointer"
                            />
                          </a>
                        </Box>
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          No PAN card front image uploaded
                        </Box>
                      )}
                    </Box>

                    {/* PAN Back Image */}
                    <Box>
                      <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "xs", md: "sm" }}>
                        Back Side
                      </Text>
                      {academyData?.panImage?.back ? (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          borderColor={borderColor}
                          bg={readOnlyBg}
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          <HStack spacing={3} mb={3}>
                            <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                              PAN Card Back Uploaded
                            </Text>
                          </HStack>
                          <a href={academyData.panImage.back} target="_blank" rel="noopener noreferrer">
                            <Image
                              src={academyData.panImage.back}
                              alt="PAN Back Image Preview"
                              maxW={{ base: "150px", md: "200px" }}
                              maxH={{ base: "100px", md: "150px" }}
                              objectFit="cover"
                              borderRadius="lg"
                              border="1px"
                              borderColor="gray.200"
                              transition="all 0.2s"
                              _hover={{
                                borderColor: "gray.400",
                                transform: "scale(1.02)"
                              }}
                              cursor="pointer"
                            />
                          </a>
                        </Box>
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          No PAN card back image uploaded
                        </Box>
                      )}
                    </Box>
                  </VStack>
                </Box>
              </VStack>
            </CardBody>
          </Card>

          {/* Aadhaar Details Section */}
          <Card 
            bg={cardBg}
            shadow="xl"
            borderRadius="lg"
            border="1px"
            borderColor="gray.200"
            overflow="hidden"
            transition="all 0.3s ease"
            _hover={{
              shadow: "2xl",
              transform: "translateY(-2px)",
              borderColor: "gray.300"
            }}
          >
            <CardBody p={{ base: 4, md: 6 }}>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Aadhaar Details
                </Heading>
              </HStack>
              
              <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* Aadhaar Number */}
                <Box>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                    Aadhaar Number
                  </FormLabel>
                  <Box
                    p={{ base: 2, md: 3 }}
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "32px", md: "36px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {academyData?.aadhaarNumber ? `****-****-${academyData.aadhaarNumber.slice(-4)}` : "No Aadhaar number provided"}
                  </Box>
                </Box>

                {/* Aadhaar Image */}
                <Box>
                  <FormLabel fontWeight="semibold" color={labelColor} mb={3} fontSize={{ base: "sm", md: "md" }}>
                    Aadhaar Card Images
                  </FormLabel>
                  
                  <VStack spacing={4} align="stretch">
                    {/* Aadhaar Front Image */}
                    <Box>
                      <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "xs", md: "sm" }}>
                        Front Side
                      </Text>
                      {academyData?.aadhaarImage?.front || academyData?.aadhaarImage ? (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          borderColor={borderColor}
                          bg={readOnlyBg}
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          <HStack spacing={3} mb={3}>
                            <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                              Aadhaar Card Front Uploaded
                            </Text>
                          </HStack>
                          <a href={academyData?.aadhaarImage?.front || academyData?.aadhaarImage} target="_blank" rel="noopener noreferrer">
                            <Image
                              src={academyData?.aadhaarImage?.front || academyData?.aadhaarImage}
                              alt="Aadhaar Front Image Preview"
                              maxW={{ base: "150px", md: "200px" }}
                              maxH={{ base: "100px", md: "150px" }}
                              objectFit="cover"
                              borderRadius="lg"
                              border="1px"
                              borderColor="gray.200"
                              transition="all 0.2s"
                              _hover={{
                                borderColor: "gray.400",
                                transform: "scale(1.02)"
                              }}
                              cursor="pointer"
                            />
                          </a>
                        </Box>
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          No Aadhaar card front image uploaded
                        </Box>
                      )}
                    </Box>

                    {/* Aadhaar Back Image */}
                    <Box>
                      <Text fontWeight="medium" color={labelColor} mb={2} fontSize={{ base: "xs", md: "sm" }}>
                        Back Side
                      </Text>
                      {academyData?.aadhaarImage?.back ? (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          borderColor={borderColor}
                          bg={readOnlyBg}
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          <HStack spacing={3} mb={3}>
                            <Text fontWeight="medium" color={textColor} fontSize={{ base: "sm", md: "md" }}>
                              Aadhaar Card Back Uploaded
                            </Text>
                          </HStack>
                          <a href={academyData.aadhaarImage.back} target="_blank" rel="noopener noreferrer">
                            <Image
                              src={academyData.aadhaarImage.back}
                              alt="Aadhaar Back Image Preview"
                              maxW={{ base: "150px", md: "200px" }}
                              maxH={{ base: "100px", md: "150px" }}
                              objectFit="cover"
                              borderRadius="lg"
                              border="1px"
                              borderColor="gray.200"
                              transition="all 0.2s"
                              _hover={{
                                borderColor: "gray.400",
                                transform: "scale(1.02)"
                              }}
                              cursor="pointer"
                            />
                          </a>
                        </Box>
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          No Aadhaar card back image uploaded
                        </Box>
                      )}
                    </Box>
                  </VStack>
                </Box>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </ScaleFade>
    );
  };

  export default KYCDetailsAcademy; 